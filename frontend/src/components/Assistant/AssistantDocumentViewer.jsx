import React, { useState, useEffect } from "react";
import {
    <PERSON>,
    Typo<PERSON>,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Alert,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    <PERSON>ton,
    Divider,
} from "@mui/material";
import {
    Description as DocumentIcon,
    Visibility as ViewIcon,
    Close as CloseIcon,
} from "@mui/icons-material";
import { beeColors } from "../Common/CustomButton";
import axiosInstance from "../../services/axiosInstance";

const AssistantDocumentViewer = ({ assistantType, assistantName }) => {
    const [documents, setDocuments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [viewDialog, setViewDialog] = useState({
        open: false,
        document: null,
        content: "",
        loading: false,
    });

    // Load documents for this assistant
    const loadDocuments = async () => {
        if (!assistantType) return;
        
        try {
            setLoading(true);
            const response = await axiosInstance.get(`/api/assistant/documents/?assistant_type=${assistantType}`);
            setDocuments(response.data);
        } catch (error) {
            console.error("Error loading documents:", error);
            setDocuments([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadDocuments();
    }, [assistantType]);

    // Handle view document
    const handleViewDocument = async (document) => {
        try {
            setViewDialog({ open: true, document, content: "", loading: true });

            const response = await axiosInstance.get(`/api/assistant/documents/${document.id}/content/`);

            setViewDialog((prev) => ({
                ...prev,
                content: response.data.content || "Không thể đọc nội dung file này.",
                loading: false,
            }));
        } catch (error) {
            setViewDialog((prev) => ({
                ...prev,
                content: "Lỗi khi tải nội dung file. Vui lòng thử lại sau.",
                loading: false,
            }));
            console.error("View document error:", error);
        }
    };

    const handleCloseViewDialog = () => {
        setViewDialog({ open: false, document: null, content: "", loading: false });
    };

    const getFileTypeColor = (fileType) => {
        switch (fileType) {
            case "pdf":
                return "error";
            case "txt":
                return "primary";
            case "md":
                return "secondary";
            default:
                return "default";
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    if (loading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                <CircularProgress size={24} />
            </Box>
        );
    }

    return (
        <Box>
            <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2, color: beeColors.neutral.main }}>
                Tài Liệu {assistantName}
            </Typography>
            
            {documents.length === 0 ? (
                <Alert severity="info" sx={{ borderRadius: "8px" }}>
                    Chưa có tài liệu nào cho {assistantName}. Bạn có thể thêm tài liệu trong System Panel.
                </Alert>
            ) : (
                <Box sx={{ maxHeight: "300px", overflowY: "auto" }}>
                    {documents.map((doc, index) => (
                        <Card 
                            key={doc.id} 
                            variant="outlined" 
                            sx={{ 
                                mb: 1, 
                                borderRadius: "8px",
                                transition: "all 0.2s ease",
                                "&:hover": {
                                    boxShadow: 2,
                                    borderColor: beeColors.primary.main,
                                },
                            }}
                        >
                            <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                                <Box sx={{ display: "flex", alignItems: "flex-start", justifyContent: "space-between" }}>
                                    <Box sx={{ flex: 1, mr: 1 }}>
                                        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                                            <DocumentIcon sx={{ mr: 1, fontSize: 16, color: beeColors.primary.main }} />
                                            <Typography 
                                                variant="body2" 
                                                fontWeight="bold"
                                                sx={{ 
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                    flex: 1,
                                                }}
                                            >
                                                {doc.title}
                                            </Typography>
                                        </Box>
                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                                            <Chip
                                                label={doc.file_type?.toUpperCase()}
                                                size="small"
                                                color={getFileTypeColor(doc.file_type)}
                                                variant="outlined"
                                            />
                                            <Typography variant="caption" color="text.secondary">
                                                {formatFileSize(doc.file_size)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                    <Tooltip title="Xem nội dung">
                                        <IconButton
                                            size="small"
                                            onClick={() => handleViewDocument(doc)}
                                            sx={{
                                                color: beeColors.primary.main,
                                                "&:hover": {
                                                    backgroundColor: `${beeColors.primary.main}10`,
                                                },
                                            }}
                                        >
                                            <ViewIcon sx={{ fontSize: 18 }} />
                                        </IconButton>
                                    </Tooltip>
                                </Box>
                            </CardContent>
                        </Card>
                    ))}
                </Box>
            )}

            {/* View Document Dialog */}
            <Dialog
                open={viewDialog.open}
                onClose={handleCloseViewDialog}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: "16px",
                        maxHeight: "80vh",
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        pb: 1,
                        borderBottom: `1px solid ${beeColors.neutral.main}20`,
                    }}
                >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <DocumentIcon sx={{ mr: 1, color: beeColors.primary.main }} />
                        <Typography variant="h6" fontWeight="bold">
                            {viewDialog.document?.title}
                        </Typography>
                    </Box>
                    <IconButton onClick={handleCloseViewDialog} size="small">
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    {viewDialog.loading ? (
                        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                whiteSpace: "pre-wrap",
                                fontFamily: "monospace",
                                fontSize: "14px",
                                lineHeight: 1.5,
                                backgroundColor: "#f5f5f5",
                                p: 2,
                                borderRadius: "8px",
                                maxHeight: "400px",
                                overflowY: "auto",
                            }}
                        >
                            {viewDialog.content}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{ p: 2, pt: 1 }}>
                    <Button onClick={handleCloseViewDialog} variant="outlined">
                        Đóng
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default AssistantDocumentViewer;
