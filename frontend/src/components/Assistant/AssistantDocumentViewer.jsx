import React, { useState, useEffect } from "react";
import {
    <PERSON>,
    Typo<PERSON>,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Alert,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    <PERSON><PERSON>,
    Divider,
} from "@mui/material";
import {
    Description as DocumentIcon,
    Visibility as ViewIcon,
    Close as CloseIcon,
    Upload as UploadIcon,
    Delete as DeleteIcon,
    Add as AddIcon,
} from "@mui/icons-material";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import { beeColors } from "../Common/CustomButton";
import CustomButton from "../Common/CustomButton";
import axiosInstance from "../../services/axiosInstance";
import { green, grey } from "@mui/material/colors";

const AssistantDocumentViewer = ({ assistantType, assistantName, showNotification }) => {
    const [documents, setDocuments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [viewDialog, setViewDialog] = useState({
        open: false,
        document: null,
        content: "",
        loading: false,
    });
    const [deleteDialog, setDeleteDialog] = useState({
        open: false,
        document: null,
        deleting: false,
    });

    // Helper function to check if file is markdown
    const isMarkdownFile = (filename) => {
        return filename?.toLowerCase().endsWith(".md");
    };

    // CodeBlock component for syntax highlighting
    const CodeBlock = ({ node, inline, className, children, ...props }) => {
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : null;

        if (inline) {
            return (
                <code
                    style={{
                        backgroundColor: "#f5f5f5",
                        padding: "2px 4px",
                        borderRadius: "4px",
                        fontFamily: '"Roboto Mono", monospace',
                        fontSize: "0.875em",
                    }}
                    {...props}
                >
                    {children}
                </code>
            );
        }

        return (
            <Box sx={{ position: "relative", mb: 2 }}>
                {/* Language Label */}
                {language && (
                    <Box
                        sx={{
                            position: "absolute",
                            top: 8,
                            left: 12,
                            zIndex: 1,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            color: "rgba(255, 255, 255, 0.7)",
                            padding: "2px 8px",
                            borderRadius: "4px",
                            fontSize: "11px",
                            fontWeight: 500,
                            textTransform: "uppercase",
                            letterSpacing: "0.5px",
                        }}
                    >
                        {language}
                    </Box>
                )}

                <SyntaxHighlighter
                    style={vscDarkPlus}
                    language={language || "text"}
                    PreTag="div"
                    customStyle={{
                        margin: 0,
                        borderRadius: "8px",
                        fontSize: "13px",
                        lineHeight: 1.45,
                        fontFamily: '"Roboto Mono", "SF Mono", Monaco, monospace',
                        paddingTop: "40px",
                    }}
                    {...props}
                >
                    {String(children).replace(/\n$/, "")}
                </SyntaxHighlighter>
            </Box>
        );
    };

    // Load documents for this assistant
    const loadDocuments = async () => {
        if (!assistantType) return;

        try {
            setLoading(true);
            const response = await axiosInstance.get(`/api/assistant/documents/?assistant_type=${assistantType}`);
            setDocuments(response.data);
        } catch (error) {
            console.error("Error loading documents:", error);
            setDocuments([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadDocuments();
    }, [assistantType]);

    // Handle multiple file upload
    const handleMultipleFileUpload = async (event) => {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        setUploading(true);
        let successCount = 0;
        let errorCount = 0;

        for (const file of files) {
            try {
                const formData = new FormData();
                formData.append("file", file);
                formData.append("assistant_type", assistantType);

                await axiosInstance.post("/api/assistant/upload-assistant/", formData, {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                });
                successCount++;
            } catch (error) {
                console.error(`Upload error for ${file.name}:`, error);
                errorCount++;
            }
        }

        // Show notification based on results
        if (successCount > 0 && errorCount === 0) {
            showNotification && showNotification(`Đã upload thành công ${successCount} tài liệu!`, "success");
        } else if (successCount > 0 && errorCount > 0) {
            showNotification &&
                showNotification(
                    `Upload thành công ${successCount} tài liệu, thất bại ${errorCount} tài liệu`,
                    "warning"
                );
        } else {
            showNotification && showNotification("Lỗi khi upload tài liệu", "error");
        }

        // Reload documents and reset input
        loadDocuments();
        event.target.value = "";
        setUploading(false);
    };

    // Handle delete document
    const handleDeleteDocument = (document) => {
        setDeleteDialog({
            open: true,
            document,
            deleting: false,
        });
    };

    const confirmDeleteDocument = async () => {
        if (!deleteDialog.document) return;

        try {
            setDeleteDialog((prev) => ({ ...prev, deleting: true }));

            await axiosInstance.delete(`/api/assistant/documents/${deleteDialog.document.id}/`);

            showNotification && showNotification("Đã xóa tài liệu thành công!", "success");
            loadDocuments(); // Reload documents
            setDeleteDialog({ open: false, document: null, deleting: false });
        } catch (error) {
            console.error("Delete document error:", error);
            showNotification && showNotification("Lỗi khi xóa tài liệu", "error");
            setDeleteDialog((prev) => ({ ...prev, deleting: false }));
        }
    };

    const cancelDeleteDocument = () => {
        setDeleteDialog({ open: false, document: null, deleting: false });
    };

    // Handle view document
    const handleViewDocument = async (document) => {
        try {
            setViewDialog({ open: true, document, content: "", loading: true });

            const response = await axiosInstance.get(`/api/assistant/documents/${document.id}/content/`);

            setViewDialog((prev) => ({
                ...prev,
                content: response.data.content || "Không thể đọc nội dung file này.",
                loading: false,
            }));
        } catch (error) {
            setViewDialog((prev) => ({
                ...prev,
                content: "Lỗi khi tải nội dung file. Vui lòng thử lại sau.",
                loading: false,
            }));
            console.error("View document error:", error);
        }
    };

    const handleCloseViewDialog = () => {
        setViewDialog({ open: false, document: null, content: "", loading: false });
    };

    // Render document content based on file type
    const renderDocumentContent = (document, content) => {
        if (isMarkdownFile(document?.title || "")) {
            return (
                <Box
                    sx={{
                        fontFamily: '"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
                        fontSize: "14px",
                        lineHeight: 1.6,
                        letterSpacing: "0.2px",
                        color: "#3c4043",
                        "& p": {
                            margin: "0 0 12px 0",
                            lineHeight: 1.65,
                            fontSize: "14px",
                            "&:last-child": {
                                marginBottom: 0,
                            },
                        },
                        "& h1, & h2, & h3, & h4, & h5, & h6": {
                            margin: "20px 0 12px 0",
                            fontWeight: 500,
                            color: "#202124",
                            letterSpacing: "-0.2px",
                            "&:first-child": {
                                marginTop: 0,
                            },
                        },
                        "& h1": { fontSize: "24px" },
                        "& h2": { fontSize: "20px" },
                        "& h3": { fontSize: "18px" },
                        "& h4": { fontSize: "16px" },
                        "& h5": { fontSize: "14px" },
                        "& h6": { fontSize: "13px" },
                        "& ul, & ol": {
                            margin: "8px 0 16px 0",
                            paddingLeft: "24px",
                        },
                        "& li": {
                            margin: "4px 0",
                            lineHeight: 1.6,
                        },
                        "& blockquote": {
                            margin: "16px 0",
                            padding: "12px 16px",
                            borderLeft: `4px solid ${beeColors.primary.main}`,
                            backgroundColor: "#f8f9fa",
                            fontStyle: "italic",
                            color: "#5f6368",
                        },
                        "& table": {
                            width: "100%",
                            borderCollapse: "collapse",
                            margin: "16px 0",
                            fontSize: "13px",
                        },
                        "& th, & td": {
                            border: "1px solid #dadce0",
                            padding: "8px 12px",
                            textAlign: "left",
                        },
                        "& th": {
                            backgroundColor: "#f8f9fa",
                            fontWeight: 500,
                            color: "#202124",
                        },
                        "& td": {
                            color: "#3c4043",
                        },
                        "& a": {
                            color: beeColors.primary.main,
                            textDecoration: "none",
                            "&:hover": {
                                textDecoration: "underline",
                            },
                        },
                        "& strong": {
                            fontWeight: 500,
                            color: "#202124",
                        },
                        "& em": {
                            fontStyle: "italic",
                            color: "#5f6368",
                        },
                    }}
                >
                    <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                            code: CodeBlock,
                        }}
                    >
                        {content}
                    </ReactMarkdown>
                </Box>
            );
        } else {
            // Plain text rendering for non-markdown files
            return (
                <Typography
                    component="pre"
                    sx={{
                        whiteSpace: "pre-wrap",
                        fontFamily: "monospace",
                        fontSize: "0.875rem",
                        lineHeight: 1.5,
                    }}
                >
                    {content}
                </Typography>
            );
        }
    };

    const getFileTypeColor = (fileType) => {
        switch (fileType) {
            case "pdf":
                return "error";
            case "txt":
                return "primary";
            case "md":
                return "secondary";
            default:
                return "default";
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return "0 Bytes";
        const k = 1024;
        const sizes = ["Bytes", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    if (loading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                <CircularProgress size={24} />
            </Box>
        );
    }

    return (
        <Box>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ color: beeColors.neutral.main }}>
                    Tài Liệu {assistantName}
                </Typography>
                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                        sx={{
                            fontSize: "0.75rem",
                            minWidth: "auto",
                            px: 1.5,
                            height: "40px",
                        }}
                        startIcon={<AddIcon />}
                    >
                        Thêm Note
                    </Button>
                    <CustomButton
                        variant="outlined"
                        component="label"
                        size="small"
                        startIcon={uploading ? <CircularProgress size={16} color="inherit" /> : <UploadIcon />}
                        disabled={uploading}
                        sx={{
                            fontSize: "0.75rem",
                            minWidth: "auto",
                            px: 1.5,
                            height: "40px",
                        }}
                    >
                        {uploading ? "Đang upload..." : "Thêm tài liệu"}
                        <input
                            type="file"
                            hidden
                            multiple
                            accept=".txt,.md,.pdf"
                            onChange={handleMultipleFileUpload}
                            disabled={uploading}
                        />
                    </CustomButton>
                </Box>
            </Box>

            {documents.length === 0 ? (
                <Alert severity="info" sx={{ borderRadius: "8px" }}>
                    Chưa có tài liệu nào cho {assistantName}. Bạn có thể thêm tài liệu trong System Panel.
                </Alert>
            ) : (
                <Box sx={{ maxHeight: "300px", overflowY: "auto" }}>
                    {documents.map((doc, index) => (
                        <Card
                            key={doc.id}
                            variant="outlined"
                            sx={{
                                mb: 1,
                                borderRadius: "8px",
                                transition: "all 0.2s ease",
                                "&:hover": {
                                    boxShadow: 2,
                                    borderColor: beeColors.primary.main,
                                },
                            }}
                        >
                            <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                                <Box
                                    sx={{ display: "flex", alignItems: "flex-start", justifyContent: "space-between" }}
                                >
                                    <Box sx={{ flex: 1, mr: 1 }}>
                                        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                                            <DocumentIcon sx={{ mr: 1, fontSize: 16, color: beeColors.primary.main }} />
                                            <Typography
                                                variant="body2"
                                                fontWeight="bold"
                                                sx={{
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                    flex: 1,
                                                }}
                                            >
                                                {doc.title}
                                            </Typography>
                                        </Box>
                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                                            <Chip
                                                label={doc.file_type?.toUpperCase()}
                                                size="small"
                                                color={getFileTypeColor(doc.file_type)}
                                                variant="outlined"
                                            />
                                            <Typography variant="caption" color="text.secondary">
                                                {formatFileSize(doc.file_size)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                    <Box sx={{ display: "flex", gap: 0.5 }}>
                                        <Tooltip title="Xem nội dung">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleViewDocument(doc)}
                                                sx={{
                                                    color: green[500],
                                                    "&:hover": {
                                                        backgroundColor: `${beeColors.primary.main}10`,
                                                    },
                                                }}
                                            >
                                                <ViewIcon sx={{ fontSize: 18 }} />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Xóa tài liệu">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleDeleteDocument(doc)}
                                                sx={{
                                                    color: grey[500],
                                                    "&:hover": {
                                                        backgroundColor: "#E74C3C10",
                                                    },
                                                }}
                                            >
                                                <DeleteIcon sx={{ fontSize: 18 }} />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    ))}
                </Box>
            )}

            {/* View Document Dialog */}
            <Dialog
                open={viewDialog.open}
                onClose={handleCloseViewDialog}
                maxWidth="md"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: "16px",
                        maxHeight: "80vh",
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        pb: 1,
                        borderBottom: `1px solid ${beeColors.neutral.main}20`,
                    }}
                >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <DocumentIcon sx={{ mr: 1, color: beeColors.primary.main }} />
                        <Typography variant="h6" fontWeight="bold">
                            {viewDialog.document?.title}
                        </Typography>
                    </Box>
                    <IconButton onClick={handleCloseViewDialog} size="small">
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    {viewDialog.loading ? (
                        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                maxHeight: "400px",
                                overflowY: "auto",
                                p: 1,
                            }}
                        >
                            {renderDocumentContent(viewDialog.document, viewDialog.content)}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{ p: 2, pt: 1 }}>
                    <CustomButton onClick={handleCloseViewDialog} variant="outlined" sx={{ height: "40px" }}>
                        Đóng
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog
                open={deleteDialog.open}
                onClose={cancelDeleteDocument}
                maxWidth="sm"
                fullWidth
                sx={{
                    "& .MuiDialog-paper": {
                        borderRadius: "16px",
                    },
                }}
            >
                <DialogTitle>
                    <Typography variant="h6" fontWeight="bold" color="error">
                        Xác nhận xóa tài liệu
                    </Typography>
                </DialogTitle>
                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        Bạn có chắc chắn muốn xóa tài liệu này không?
                    </Typography>
                    <Typography variant="body2" fontWeight="bold" color="text.secondary">
                        {deleteDialog.document?.title}
                    </Typography>
                    <Alert severity="warning" sx={{ mt: 2, borderRadius: "8px" }}>
                        Hành động này không thể hoàn tác!
                    </Alert>
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 3 }}>
                    <Button
                        onClick={cancelDeleteDocument}
                        variant="outlined"
                        color="inherit"
                        disabled={deleteDialog.deleting}
                        sx={{ borderRadius: "12px", height: "40px", marginRight: "10px" }}
                    >
                        Hủy
                    </Button>
                    <Button
                        onClick={confirmDeleteDocument}
                        variant="contained"
                        color="error"
                        disabled={deleteDialog.deleting}
                        startIcon={
                            deleteDialog.deleting ? <CircularProgress size={16} color="inherit" /> : <DeleteIcon />
                        }
                        sx={{ borderRadius: "12px", height: "40px" }}
                    >
                        {deleteDialog.deleting ? "Đang xóa..." : "Xóa"}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default AssistantDocumentViewer;
