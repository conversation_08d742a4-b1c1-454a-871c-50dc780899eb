import React, { useState, useEffect, useRef } from "react";
import {
    Box,
    TextField,
    IconButton,
    Typography,
    Paper,
    CircularProgress,
    Chip,
    Tooltip,
    Switch,
    FormControlLabel,
} from "@mui/material";
import {
    Send as SendIcon,
    Code as CodeIcon,
    Description as DocumentIcon,
    Settings as SettingsIcon,
    Add as AddIcon,
    ContentCopy as CopyIcon,
    Refresh as RetryIcon,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import axiosInstance from "../../services/axiosInstance";
import { grey } from "@mui/material/colors";
import { getOrCreateSession, loadSessionMessages, isAdminOrSupervisor } from "../../utils/sessionUtils";

// Styled Components
const ChatContainer = styled(Paper)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    height: "100%",
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.spacing(2),
    overflow: "hidden",
    boxShadow: theme.shadows[3],
}));

const ChatHeader = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: 64,
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
    flex: 1,
    padding: theme.spacing(1),
    overflowY: "auto",
    backgroundColor: theme.palette.background.default,
    "&::-webkit-scrollbar": {
        width: "6px",
    },
    "&::-webkit-scrollbar-track": {
        background: theme.palette.grey[100],
    },
    "&::-webkit-scrollbar-thumb": {
        background: theme.palette.grey[400],
        borderRadius: "3px",
    },
}));

const MessageBubble = styled(Box)(({ theme, isUser }) => ({
    display: "flex",
    justifyContent: isUser ? "flex-end" : "flex-start",
    marginBottom: theme.spacing(1),
    "& .message-content": {
        maxWidth: "80%",
        padding: theme.spacing(1.5),
        borderRadius: theme.spacing(2),
        // backgroundColor: isUser ? theme.palette.primary.main : theme.palette.grey[100],
        color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
        wordBreak: "break-word",
    },
}));

const InputContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    backgroundColor: theme.palette.background.paper,
    borderTop: `1px solid ${theme.palette.divider}`,
    display: "flex",
    alignItems: "flex-end",
    gap: theme.spacing(1),
}));

const MarkdownContainer = styled(Box)(({ theme }) => ({
    fontFamily: '"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
    fontSize: "14px",
    lineHeight: 1.6,
    letterSpacing: "0.2px",
    color: "#3c4043",
    "& h1, & h2, & h3, & h4, & h5, & h6": {
        fontFamily: '"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
        fontWeight: 500,
        color: "#202124",
        letterSpacing: "-0.2px",
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(1),
    },
    "& p": {
        marginBottom: theme.spacing(1.5),
        lineHeight: 1.65,
    },
    "& ul, & ol": {
        paddingLeft: theme.spacing(3),
        marginBottom: theme.spacing(1.5),
    },
    "& li": {
        marginBottom: theme.spacing(0.5),
    },
    "& blockquote": {
        borderLeft: `4px solid ${theme.palette.primary.main}`,
        paddingLeft: theme.spacing(2),
        margin: theme.spacing(2, 0),
        backgroundColor: theme.palette.grey[50],
        padding: theme.spacing(1.5),
        borderRadius: theme.spacing(1),
    },
    "& table": {
        width: "100%",
        borderCollapse: "collapse",
        marginBottom: theme.spacing(2),
        "& th, & td": {
            border: `1px solid ${theme.palette.divider}`,
            padding: theme.spacing(1),
            textAlign: "left",
        },
        "& th": {
            backgroundColor: theme.palette.grey[100],
            fontWeight: 500,
        },
    },
}));

// Custom CodeBlock component
const CodeBlock = ({ node, inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || "");
    const language = match ? match[1] : "";
    const [copied, setCopied] = useState(false);

    const handleCopy = async () => {
        try {
            const codeText = String(children).replace(/\n$/, "");
            await navigator.clipboard.writeText(codeText);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error("Failed to copy code:", err);
        }
    };

    if (language === "") {
        return (
            <Box
                component="code"
                sx={{
                    fontFamily:
                        '"Roboto Mono", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
                    fontSize: "13px",
                    backgroundColor: "#f6f8fa",
                    color: "#d73a49",
                    padding: "2px 6px",
                    borderRadius: "4px",
                    border: "1px solid #e1e4e8",
                }}
                {...props}
            >
                {children}
            </Box>
        );
    }

    return (
        <Box sx={{ margin: "16px 0", position: "relative" }}>
            {/* Copy Button */}
            <Tooltip title={copied ? "Đã copy!" : "Copy code"}>
                <IconButton
                    onClick={handleCopy}
                    sx={{
                        position: "absolute",
                        top: 8,
                        right: 8,
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        color: "rgba(255, 255, 255, 0.7)",
                        width: 32,
                        height: 32,
                        "&:hover": {
                            backgroundColor: "rgba(255, 255, 255, 0.2)",
                            color: "rgba(255, 255, 255, 0.9)",
                        },
                        transition: "all 0.2s ease",
                    }}
                >
                    <CopyIcon sx={{ fontSize: 16 }} />
                </IconButton>
            </Tooltip>

            {/* Language Label */}
            {language && (
                <Box
                    sx={{
                        position: "absolute",
                        top: 8,
                        left: 12,
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        color: "rgba(255, 255, 255, 0.7)",
                        padding: "2px 8px",
                        borderRadius: "4px",
                        fontSize: "11px",
                        fontWeight: 500,
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                    }}
                >
                    {language}
                </Box>
            )}

            <SyntaxHighlighter
                style={vscDarkPlus}
                language={language || "text"}
                customStyle={{
                    margin: 0,
                    borderRadius: "8px",
                    fontSize: "13px",
                    lineHeight: 1.45,
                    fontFamily:
                        '"Roboto Mono", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
                    paddingTop: "40px", // Add padding to make room for buttons
                    borderRadius: "20px",
                }}
                {...props}
            >
                {String(children).replace(/\n$/, "")}
            </SyntaxHighlighter>
        </Box>
    );
};

const ChatBox = ({
    title = "BeE Assistant",
    height = "600px",
    width = "100%",
    pythonCode = "",
    blocksJson = "",
    contextData = {},
    onSettingsClick,
    showSettings = true,
    showDocuments = true,
    showCodeContext = true,
    placeholder = "Nhập tin nhắn của bạn...",
    systemPrompt = "",
    assistantType = "", // New prop for assistant type
    persistSession = true, // New prop to enable session persistence
    onNewSession, // Callback for new session button
    forceNewSession = 0, // Counter to force new session creation
    userBgColor = "#1976d2",
}) => {
    const [messages, setMessages] = useState([]);
    const [inputMessage, setInputMessage] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [sessionId, setSessionId] = useState(null);
    const [useDocuments, setUseDocuments] = useState(true);
    const [useCodeContext, setUseCodeContext] = useState(true);
    const messagesEndRef = useRef(null);

    // Generate unique storage key for this chat instance
    const storageKey = `bee-chat-session-${title.replace(/\s+/g, "-").toLowerCase()}`;

    // Auto-scroll to bottom when new messages arrive
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Load persisted session or get most recent session or create new one
    useEffect(() => {
        const loadOrCreateSession = async () => {
            // Check if user has permission to persist sessions
            const hasPermission = await isAdminOrSupervisor();

            if (persistSession && hasPermission) {
                // Admin/Supervisor: Try to load existing session from localStorage first
                const savedSessionId = localStorage.getItem(storageKey);
                if (savedSessionId) {
                    setSessionId(savedSessionId);
                    const messages = await loadSessionMessages(savedSessionId);
                    if (messages.length === 0) {
                        // Session might not exist anymore, clear from localStorage
                        localStorage.removeItem(storageKey);
                    } else {
                        setMessages(messages);
                        return;
                    }
                }
            }

            // Regular user OR admin/supervisor with no persisted session: get or create session
            try {
                const getAssistantType = (promptKey) => {
                    const mapping = {
                        bee_assistant_prompt: "bee_assistant",
                        bee_ide_prompt: "bee_ide",
                        python_prompt: "python",
                        teacher_prompt: "teacher",
                    };
                    return mapping[promptKey] || "bee_assistant";
                };
                const assistantType = getAssistantType(selectedPrompt);
                console.log(assistantType);
                const sessionId = await getOrCreateSession(assistantType);
                setSessionId(sessionId);

                // Load messages from the session (only for admin/supervisor)
                if (hasPermission) {
                    const messages = await loadSessionMessages(sessionId);
                    setMessages(messages);
                } else {
                    console.error("Created new session for regular user:", sessionId);
                }
            } catch (error) {
                console.error("Error loading or creating session:", error);
                // Fallback to creating new session
                await createNewSession();
            }
        };

        loadOrCreateSession();
    }, []);

    // Save session ID to localStorage when it changes (only for admin/supervisor)
    useEffect(() => {
        const saveSession = async () => {
            if (persistSession && sessionId) {
                const hasPermission = await isAdminOrSupervisor();
                if (hasPermission) {
                    localStorage.setItem(storageKey, sessionId);
                } else {
                    console.error("Session not saved - regular user");
                }
            }
        };
        saveSession();
    }, [sessionId, persistSession, storageKey]);

    // Handle force new session - use ref to track if we're already processing
    const processingNewSession = useRef(false);

    useEffect(() => {
        if (forceNewSession > 0 && !processingNewSession.current) {
            processingNewSession.current = true;

            // Clear messages and session immediately
            if (persistSession && sessionId) {
                localStorage.removeItem(storageKey);
            }
            setMessages([]);
            setSessionId(null);

            // Create new session directly
            const createNew = async () => {
                try {
                    const response = await axiosInstance.post("/api/assistant/chat-sessions/", {
                        title: `Chat ${new Date().toLocaleString()}`,
                    });
                    setSessionId(response.data.id);
                } catch (error) {
                    console.error("Error creating new session:", error);
                } finally {
                    processingNewSession.current = false;
                }
            };

            createNew();
        }
    }, [forceNewSession]);

    const createNewSession = async () => {
        try {
            const response = await axiosInstance.post("/api/assistant/chat-sessions/", {
                title: `Chat ${new Date().toLocaleString()}`,
            });
            setSessionId(response.data.id);
        } catch (error) {
            console.error("Error creating session:", error);
        }
    };

    const clearSession = async () => {
        if (persistSession && sessionId) {
            const hasPermission = await isAdminOrSupervisor();
            if (hasPermission) {
                localStorage.removeItem(storageKey);
            }
        }
        setMessages([]);
        setSessionId(null);
        await createNewSession();

        // Call parent callback if provided
        if (onNewSession) {
            onNewSession();
        }
    };

    const handleSendMessage = async () => {
        if (!inputMessage.trim() || isLoading) return;

        const userMessage = inputMessage.trim();
        setInputMessage("");
        setIsLoading(true);

        // Add user message to chat
        const newUserMessage = {
            role: "user",
            content: userMessage,
            timestamp: new Date(),
        };
        setMessages((prev) => [...prev, newUserMessage]);

        try {
            // Prepare request data
            const requestData = {
                message: userMessage,
                session_id: sessionId,
                use_documents: useDocuments,
            };

            // Add Python code context if available and enabled
            if (useCodeContext && pythonCode) {
                requestData.python_code = pythonCode;
            }

            // Add Blockly blocks JSON context if available and enabled
            if (useCodeContext && blocksJson) {
                requestData.blocks_json = blocksJson;
            }

            // Add additional context data
            if (contextData && Object.keys(contextData).length > 0) {
                requestData.context_data = contextData;
            }

            // Add custom system prompt if provided
            if (systemPrompt) {
                requestData.system_prompt = systemPrompt;
            }

            // Add assistant type if provided
            if (assistantType) {
                requestData.assistant_type = assistantType;
            }

            const response = await axiosInstance.post("/api/assistant/chat/", requestData);

            // Add AI response to chat
            const aiMessage = {
                role: "assistant",
                content: response.data.message,
                timestamp: new Date(),
                tokens_used: response.data.tokens_used,
                context_documents: response.data.context_documents,
            };
            setMessages((prev) => [...prev, aiMessage]);
        } catch (error) {
            console.error("Error sending message:", error);

            // Add error message to chat
            const errorMessage = {
                role: "assistant",
                content: "Xin lỗi, đã có lỗi xảy ra khi xử lý tin nhắn của bạn. Vui lòng thử lại sau.",
                timestamp: new Date(),
                isError: true,
            };
            setMessages((prev) => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyPress = (event) => {
        if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();
            handleSendMessage();
        }
    };

    const handleRetryMessage = async (messageIndex) => {
        // Find the user message that corresponds to this error message
        const userMessageIndex = messageIndex - 1;
        if (userMessageIndex < 0 || messages[userMessageIndex].role !== "user") {
            console.error("Cannot find original user message to retry");
            return;
        }

        const userMessage = messages[userMessageIndex];

        // Remove the error message and set loading
        setMessages((prev) => prev.slice(0, messageIndex));
        setIsLoading(true);

        try {
            // Prepare request data
            const requestData = {
                message: userMessage.content,
                session_id: sessionId,
                use_documents: useDocuments,
            };

            // Add Python code context if available and enabled
            if (useCodeContext && pythonCode) {
                requestData.python_code = pythonCode;
            }

            // Add Blockly blocks JSON context if available and enabled
            if (useCodeContext && blocksJson) {
                requestData.blocks_json = blocksJson;
            }

            // Add additional context data
            if (contextData && Object.keys(contextData).length > 0) {
                requestData.context_data = contextData;
            }

            // Add custom system prompt if provided
            if (systemPrompt) {
                requestData.system_prompt = systemPrompt;
            }

            // Add assistant type if provided
            if (assistantType) {
                requestData.assistant_type = assistantType;
            }

            const response = await axiosInstance.post("/api/assistant/chat/", requestData);

            // Add AI response to chat
            const aiMessage = {
                role: "assistant",
                content: response.data.message,
                timestamp: new Date(),
                tokens_used: response.data.tokens_used,
                context_documents: response.data.context_documents,
            };
            setMessages((prev) => [...prev, aiMessage]);
        } catch (error) {
            console.error("Retry error:", error);

            // Add error message to chat
            const errorMessage = {
                role: "assistant",
                content: "Xin lỗi, đã có lỗi xảy ra khi xử lý tin nhắn của bạn. Vui lòng thử lại sau.",
                timestamp: new Date(),
                isError: true,
            };
            setMessages((prev) => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
        }
    };

    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString("vi-VN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    return (
        <ChatContainer sx={{ height, width }}>
            {/* Header */}
            <ChatHeader sx={{ display: "none" }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {title}
                    </Typography>
                    {pythonCode && showCodeContext && (
                        <Chip
                            icon={<CodeIcon />}
                            label="Python Context"
                            size="small"
                            color="secondary"
                            variant="outlined"
                        />
                    )}
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    {showDocuments && (
                        <Tooltip title="Sử dụng tài liệu">
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={useDocuments}
                                        onChange={(e) => setUseDocuments(e.target.checked)}
                                        size="small"
                                        color="secondary"
                                    />
                                }
                                label={<DocumentIcon />}
                                sx={{ margin: 0, color: "inherit" }}
                            />
                        </Tooltip>
                    )}

                    {showCodeContext && pythonCode && (
                        <Tooltip title="Sử dụng Python code context">
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={useCodeContext}
                                        onChange={(e) => setUseCodeContext(e.target.checked)}
                                        size="small"
                                        color="secondary"
                                    />
                                }
                                label={<CodeIcon />}
                                sx={{ margin: 0, color: "inherit" }}
                            />
                        </Tooltip>
                    )}

                    {persistSession && !onNewSession && (
                        <Tooltip title="Chat mới">
                            <IconButton onClick={clearSession} sx={{ color: "inherit" }} size="small">
                                <AddIcon />
                            </IconButton>
                        </Tooltip>
                    )}

                    {showSettings && onSettingsClick && (
                        <Tooltip title="Cài đặt">
                            <IconButton onClick={onSettingsClick} sx={{ color: "inherit" }} size="small">
                                <SettingsIcon />
                            </IconButton>
                        </Tooltip>
                    )}
                </Box>
            </ChatHeader>

            {/* Messages */}
            <MessagesContainer>
                {messages.length === 0 && (
                    <Box sx={{ textAlign: "center", py: 4, color: "text.secondary" }}>
                        <Typography variant="body2">
                            Chào mừng bạn đến với BeE Assistant! Hãy bắt đầu cuộc trò chuyện.
                        </Typography>
                    </Box>
                )}

                {messages.map((message, index) => (
                    <MessageBubble key={index} isUser={message.role === "user"}>
                        <Box
                            sx={{
                                flex: 1,
                                order: message.role === "user" ? 1 : 1,
                                display: "flex",
                                flexDirection: "column",
                                alignItems: message.role === "user" ? "flex-end" : "flex-start",
                            }}
                        >
                            <Box
                                className="message-content"
                                sx={{ backgroundColor: message.role === "user" ? userBgColor : grey[100] }}
                            >
                                {message.role === "user" ? (
                                    <>
                                        <Typography variant="body2">{message.content}</Typography>
                                    </>
                                ) : (
                                    <>
                                        <MarkdownContainer>
                                            <ReactMarkdown
                                                remarkPlugins={[remarkGfm]}
                                                components={{
                                                    code: CodeBlock,
                                                }}
                                            >
                                                {message.content}
                                            </ReactMarkdown>
                                        </MarkdownContainer>
                                        <Box
                                            sx={{
                                                display: "flex",
                                                justifyContent: "start",
                                                alignItems: "center",
                                                gap: 1,
                                            }}
                                        >
                                            {message.isError && (
                                                <Tooltip title="Thử lại tin nhắn">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleRetryMessage(index)}
                                                        sx={{
                                                            color: grey[500],
                                                            "&:hover": {
                                                                color: "#1976d2",
                                                                backgroundColor: "rgba(25, 118, 210, 0.04)",
                                                            },
                                                            width: 20,
                                                            height: 20,
                                                        }}
                                                    >
                                                        <RetryIcon sx={{ fontSize: 14 }} />
                                                    </IconButton>
                                                </Tooltip>
                                            )}
                                        </Box>
                                    </>
                                )}
                            </Box>
                            <Box
                                className="message-content"
                                sx={{
                                    display: "flex",
                                    justifyContent: message.role === "user" ? "flex-end" : "flex-start",
                                    paddingTop: "5px !important",
                                }}
                            >
                                <Typography
                                    variant="caption"
                                    sx={{
                                        mt: "1px",
                                        color: grey[400],
                                        fontSize: "0.75rem",
                                    }}
                                >
                                    {formatTimestamp(message.timestamp)}
                                </Typography>
                            </Box>
                        </Box>
                    </MessageBubble>
                ))}

                {isLoading && (
                    <MessageBubble isUser={false}>
                        <Box
                            className="message-content"
                            sx={{ display: "flex", alignItems: "center", gap: 1, bgcolor: grey[100] }}
                        >
                            <CircularProgress size={16} />
                            <Typography variant="body2">Đang suy nghĩ...</Typography>
                        </Box>
                    </MessageBubble>
                )}

                <div ref={messagesEndRef} />
            </MessagesContainer>

            {/* Input */}
            <InputContainer>
                <TextField
                    fullWidth
                    multiline
                    maxRows={4}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={placeholder}
                    disabled={isLoading}
                    variant="outlined"
                    size="small"
                    sx={{
                        "& .MuiOutlinedInput-root": {
                            borderRadius: 3,
                        },
                    }}
                />
                <IconButton
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || isLoading}
                    color="primary"
                    sx={{
                        backgroundColor: userBgColor,
                        color: "primary.contrastText",
                        "&:hover": {
                            backgroundColor: "primary.dark",
                        },
                        "&:disabled": {
                            backgroundColor: "grey.300",
                        },
                    }}
                >
                    <SendIcon />
                </IconButton>
            </InputContainer>
        </ChatContainer>
    );
};

export default ChatBox;
