import React from "react";
import { Box, Typography, Paper } from "@mui/material";
import Grid from "@mui/material/Grid2";
import AssistantDocumentViewer from "./AssistantDocumentViewer";
import { beeColors } from "../Common/CustomButton";

const ChatPlaygroundDemo = () => {
    return (
        <Box sx={{ p: 4, backgroundColor: beeColors.background.main, minHeight: "100vh" }}>
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 4, color: beeColors.neutral.main }}>
                Chat Playground Demo - Document Viewer
            </Typography>
            
            <Grid container spacing={3} sx={{ height: "calc(100vh - 200px)" }}>
                {/* Mock Chat Area */}
                <Grid size={{ xs: 12, md: 8 }} sx={{ height: "100%" }}>
                    <Paper
                        sx={{
                            height: "100%",
                            borderRadius: "16px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: beeColors.background.paper,
                        }}
                    >
                        <Typography variant="h6" color="text.secondary">
                            Mock Chat Area
                        </Typography>
                    </Paper>
                </Grid>

                {/* Context Panel with Document Viewer */}
                <Grid size={{ xs: 12, md: 4 }} sx={{ height: "100%", display: "flex", flexDirection: "column", gap: 2 }}>
                    {/* Chat Session Info Card - 50% height */}
                    <Paper
                        sx={{
                            borderRadius: "16px",
                            height: "50%",
                            display: "flex",
                            flexDirection: "column",
                            p: 2,
                        }}
                    >
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                            Thông Tin Phiên Chat
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Mock session info content...
                        </Typography>
                    </Paper>

                    {/* Assistant Documents Card - 50% height */}
                    <Paper
                        sx={{
                            borderRadius: "16px",
                            height: "50%",
                            display: "flex",
                            flexDirection: "column",
                            p: 2,
                        }}
                    >
                        <AssistantDocumentViewer
                            assistantType="bee_assistant"
                            assistantName="BeE Assistant"
                        />
                    </Paper>
                </Grid>
            </Grid>
        </Box>
    );
};

export default ChatPlaygroundDemo;
